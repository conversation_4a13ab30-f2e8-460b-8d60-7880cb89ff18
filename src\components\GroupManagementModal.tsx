import React, { useState, useEffect, useCallback } from 'react';
import { Modal, Table, Input, Button, Space, message, Popconfirm, Form, Tag, Select } from 'antd';
import { SearchOutlined, ReloadOutlined, EditOutlined, DeleteOutlined, PlusOutlined, TeamOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { TaskBasicGroupFormData, TaskGroupSearchParams } from '../types/task';
import { TaskService } from '@/services/taskService';
import { httpClient } from '@/services/http/client';

// 生成1000条模拟数据 - 移到组件外部避免重复生成
const generateMockData = (): TaskBasicGroupFormData[] => {
  const groupNames = [
    '数据同步',
    '报表生成',
    '数据清理',
    '系统监控',
    '备份任务',
    '日志分析',
    '性能优化',
    '安全检查',
    '数据迁移',
    '接口测试',
    '用户管理',
    '权限控制',
    '消息推送',
    '文件处理',
    '图片压缩',
    '视频转码',
    '邮件发送',
    '短信通知',
    '支付处理',
    '订单同步',
    '库存管理',
    '价格更新',
    '促销活动',
    '客户服务',
    '数据统计',
    '业务分析',
    '风险控制',
    '合规检查',
    '审计日志',
    '系统升级',
    '配置管理',
    '环境部署',
    '代码发布',
    '版本控制',
    '质量检测',
    '自动化测试',
    '压力测试',
    '兼容性测试',
    '安全扫描',
    '漏洞修复',
    '数据备份',
    '灾难恢复',
    '容量规划',
    '资源监控',
    '告警处理',
    '故障排查',
    '性能调优',
    '缓存清理',
    '索引优化',
    '查询优化',
  ];

  const data: TaskBasicGroupFormData[] = [];

  for (let i = 1; i <= 1000; i++) {
    const baseNameIndex = (i - 1) % groupNames.length;
    const suffix = Math.floor((i - 1) / groupNames.length) + 1;
    const name = suffix === 1 ? groupNames[baseNameIndex] : `${groupNames[baseNameIndex]}_${suffix}`;

    // 生成随机日期（最近一年内）
    const createDate = new Date();
    createDate.setDate(createDate.getDate() - Math.floor(Math.random() * 365));
    const createTime = createDate.toISOString().slice(0, 19).replace('T', ' ');

    // 更新时间在创建时间之后
    const updateDate = new Date(createDate);
    updateDate.setDate(updateDate.getDate() + Math.floor(Math.random() * 30));
    const updateTime = updateDate.toISOString().slice(0, 19).replace('T', ' ');

    let used = true;
    if (i % 2 === 0) {
      used = false;
    }

    data.push({
      id: i,
      name,
      is_used: used,
      create_time: createTime,
      update_time: updateTime,
    });
  }

  return data;
};

// 全局模拟数据，只生成一次
const mockData: TaskBasicGroupFormData[] = generateMockData();

// 分组表单组件
interface GroupFormModalProps {
  visible: boolean;
  editingData?: TaskBasicGroupFormData;
  onCancel: () => void;
  onSubmit: (data: Omit<TaskBasicGroupFormData, 'id' | 'create_time' | 'update_time'>) => void;
}

const GroupFormModal: React.FC<GroupFormModalProps> = ({ visible, editingData, onCancel, onSubmit }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible && editingData) {
      form.setFieldsValue({
        id: editingData.id,
        name: editingData.name,
      });
    } else if (visible) {
      form.resetFields();
    }
  }, [visible, editingData, form]);

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      await new Promise(resolve => setTimeout(resolve, 500));
      console.log(values);
      
      onSubmit(values);
      message.success(editingData ? '编辑分组成功' : '新增分组成功');
      onCancel();
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'errorFields' in error) {
        message.error('请检查表单输入');
      } else {
        message.error('操作失败: ' + String(error));
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal title={editingData ? '编辑分组' : '新增分组'} open={visible} onCancel={onCancel} onOk={handleSubmit} confirmLoading={loading} maskClosable={false} width={500}>
      <Form form={form} layout='vertical' style={{ marginTop: 20 }}>
        <Form.Item
          label='分组名称'
          name='name'
          rules={[
            { required: true, message: '请输入分组名称' },
            { min: 2, max: 50, message: '分组名称长度应在2-50个字符之间' },
            { pattern: /^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/, message: '分组名称只能包含中英文、数字、下划线和横线' },
          ]}
        >
          <Input placeholder='请输入分组名称' />
        </Form.Item>
      </Form>
    </Modal>
  );
};

interface GroupManagementModalProps {
  visible: boolean;
  onCancel: () => void;
}

const GroupManagementModal: React.FC<GroupManagementModalProps> = ({ visible, onCancel }) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<TaskBasicGroupFormData[]>([]);
  const [searchText, setSearchText] = useState('');
  const [usedFilter, setUsedFilter] = useState<boolean | undefined>(undefined); // 使用状态筛选
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  // 表单相关状态
  const [formVisible, setFormVisible] = useState(false);
  const [editingGroup, setEditingGroup] = useState<TaskBasicGroupFormData | undefined>(undefined);

  // 多选相关状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<TaskBasicGroupFormData[]>([]);

  // 加载数据 - 移除 pagination 依赖避免无限循环
  const loadData = useCallback(async (customParams?: Partial<TaskGroupSearchParams>) => {
    setLoading(true);

    try {
      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 500));

      const params = {
        // ...pagination,
        ...customParams,
      };

      console.log(params);

      const response = await TaskService.getTaskGroups(params);
      console.log(response);

      setData(response.data);
      // setTotal(response.total);

      // 使用传入的参数或默认值
      const current = customParams?.current || 1;
      const pageSize = customParams?.pageSize || 20;

      setPagination({
        current,
        pageSize,
        total: response.total,
      });

      message.success('数据加载成功');
    } catch (error) {
      message.error('数据加载失败: ' + error);
    } finally {
      setLoading(false);
    }
  }, []); // 空依赖数组，避免无限循环

  // 搜索
  const handleSearch = useCallback(() => {
    // 搜索时清空选择状态
    handleClearSelection();
    loadData({
      name: searchText,
      is_used: usedFilter,
      current: 1,
      pageSize: pagination.pageSize,
    });
  }, [searchText, usedFilter, pagination.pageSize, loadData]);

  // 重置搜索
  const handleReset = useCallback(() => {
    setSearchText('');
    setUsedFilter(undefined);
    // 重置时清空选择状态
    handleClearSelection();
    loadData({
      current: 1,
      pageSize: pagination.pageSize,
    });
  }, [pagination.pageSize, loadData]);

  // 编辑分组
  const handleEdit = (record: TaskBasicGroupFormData) => {
    // 检查分组是否正在使用中
    // if (record.is_used) {
    //   message.error(`分组 "${record.name}" 正在使用中，无法编辑`);
    //   return;
    // }

    setEditingGroup(record);
    setFormVisible(true);
  };

  // 删除分组
  const handleDelete = async (record: TaskBasicGroupFormData) => {
    // 检查分组是否正在使用中
    if (record.is_used) {
      message.error(`分组 "${record.name}" 正在使用中，无法删除`);
      return;
    }

    try {
      // 模拟删除API请求
      await new Promise(resolve => setTimeout(resolve, 300));
      message.success(`删除分组 "${record.name}" 成功`);
      loadData({
        name: searchText,
        is_used: usedFilter,
        current: pagination.current,
        pageSize: pagination.pageSize,
      });
    } catch (error) {
      message.error('删除失败: ' + error);
    }
  };

  // 新增分组
  const handleAdd = () => {
    setEditingGroup(undefined);
    setFormVisible(true);
  };

  // 表单提交处理
  const handleFormSubmit = async (formData: TaskBasicGroupFormData) => {
    try {
      if (editingGroup) {
        // 更新
        const res = await httpClient.post('/api/v1/task/group/update', formData);
        console.log(res);


        message.success('编辑分组成功');
      } else {
        // 新增
        const res = await httpClient.post('/api/v1/task/group/add', formData);
        console.log(res);

        message.success('新增分组成功');
      }

      // 重新加载数据
      loadData({
        name: searchText,
        is_used: usedFilter,
        current: pagination.current,
        pageSize: pagination.pageSize,
      });

      setFormVisible(false);
      setEditingGroup(undefined);
    } catch (error) {
      message.error('操作失败: ' + error);
    }
  };

  // 表单取消处理
  const handleFormCancel = () => {
    setFormVisible(false);
    setEditingGroup(undefined);
  };

  // 多选处理 - 支持跨页选择
  const handleSelectionChange = (newSelectedRowKeys: React.Key[]) => {
    // 获取当前页面的所有行键
    const currentPageKeys = data.map(item => item.id);

    // 保留非当前页面的已选择项（这些项目不在当前页面，所以不会被 Antd 的 onChange 影响）
    const preservedKeys = selectedRowKeys.filter(key => !currentPageKeys.includes(Number(key)));
    const preservedRows = selectedRows.filter(row => !currentPageKeys.includes(row.id));

    // 获取当前页面选中的行数据（基于 newSelectedRowKeys，这是 Antd 传递的当前页面的选择状态）
    const currentPageSelectedRows = data.filter(item => newSelectedRowKeys.includes(item.id));

    // 合并保留的选择和当前页面的新选择
    const finalSelectedKeys = [...preservedKeys, ...newSelectedRowKeys];
    const finalSelectedRows = [...preservedRows, ...currentPageSelectedRows];

    setSelectedRowKeys(finalSelectedKeys);
    setSelectedRows(finalSelectedRows);
  };

  // 取消选择
  const handleClearSelection = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRows.length === 0) {
      message.warning('请先选择要删除的分组');
      return;
    }

    // 检查是否有正在使用中的分组
    const usedGroups = selectedRows.filter(row => row.is_used);
    if (usedGroups.length > 0) {
      const usedNames = usedGroups.map(group => group.name).join(', ');
      message.error(`以下分组正在使用中，无法删除: ${usedNames}`);
      return;
    }

    try {
      // 模拟批量删除API请求
      await new Promise(resolve => setTimeout(resolve, 800));

      // 从模拟数据中删除选中的项
      const selectedIds = selectedRows.map(row => row.id);
      const deletedNames = selectedRows.map(row => row.name);

      // 从 mockData 中移除选中的项
      for (let i = mockData.length - 1; i >= 0; i--) {
        if (selectedIds.includes(mockData[i].id)) {
          mockData.splice(i, 1);
        }
      }

      message.success(`成功删除 ${selectedRows.length} 个分组: ${deletedNames.join(', ')}`);

      // 清空选择
      handleClearSelection();

      // 重新加载数据
      loadData({
        name: searchText,
        is_used: usedFilter,
        current: pagination.current,
        pageSize: pagination.pageSize,
      });
    } catch (error) {
      message.error('批量删除失败: ' + error);
    }
  };

  // 分页变化处理
  const handleTableChange = useCallback(
    (page: number, pageSize: number) => {
      loadData({
        name: searchText,
        is_used: usedFilter,
        current: page,
        pageSize,
      });
    },
    [searchText, usedFilter, loadData]
  );

  // 初始化数据
  useEffect(() => {
    if (visible) {
      loadData();
    } else {
      // 模态框关闭时清空选择状态
      handleClearSelection();
    }
  }, [visible, loadData]);

  // 表格列定义
  const columns: ColumnsType<TaskBasicGroupFormData> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 50,
    },
    {
      title: '分组名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '被使用',
      dataIndex: 'is_used',
      key: 'is_used',
      width: 80,
      render: (is_used: boolean) => <Tag color={is_used ? 'green' : 'default'}>{is_used ? '是' : '否'}</Tag>,
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      width: 200,
      ellipsis: true,
    },
    {
      title: '更新时间',
      dataIndex: 'update_time',
      key: 'update_time',
      width: 200,
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 160,
      render: (_, record) => (
        <Space size='small'>
          <Button
            type='link'
            size='small'
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            // disabled={record.is_used}
            title={record.is_used ? '该分组正在使用中，无法编辑' : '编辑分组'}
          >
            编辑
          </Button>
          <Popconfirm
            title='确认删除'
            description={record.is_used ? '该分组正在使用中，无法删除' : `确定要删除分组 "${record.name}" 吗？`}
            onConfirm={() => handleDelete(record)}
            okText='确认'
            cancelText='取消'
            disabled={record.is_used}
          >
            <Button type='link' size='small' danger icon={<DeleteOutlined />} disabled={record.is_used} title={record.is_used ? '该分组正在使用中，无法删除' : '删除分组'}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Modal
      title={
        <div className='flex items-center gap-2'>
          <TeamOutlined className='text-blue-500' />
          <span>分组管理</span>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      width={1200}
      footer={null}
      maskClosable={false}
      styles={{
        body: { padding: '20px 24px' },
      }}
    >
      {/* 搜索区域 */}
      <div className='mb-6 p-4 bg-gray-50 rounded-lg'>
        <div className='flex justify-between items-center'>
          <div className='flex items-center gap-3'>
            <Input
              placeholder='请输入分组名称'
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
              onPressEnter={handleSearch}
              style={{ width: 280 }}
              allowClear
              className='shadow-sm'
            />
            <Select
              placeholder='选择使用状态'
              value={usedFilter}
              onChange={setUsedFilter}
              style={{ width: 150 }}
              allowClear
              className='shadow-sm'
              options={[
                // { label: '全部', value: undefined },
                { label: '已使用', value: true },
                { label: '未使用', value: false },
              ]}
            />
          </div>
          <div>
            <Space>
              <Button type='primary' icon={<SearchOutlined />} onClick={handleSearch} className='shadow-sm'>
                搜索
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset} className='shadow-sm'>
                重置
              </Button>
              <Button
                type='primary'
                icon={<PlusOutlined />}
                onClick={handleAdd}
                className='shadow-sm'
                style={{
                  background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                  border: 'none',
                }}
              >
                新增分组
              </Button>
            </Space>
          </div>
        </div>
      </div>

      {/* 选择提示栏 */}
      {selectedRowKeys.length > 0 && (
        <div className='mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg flex justify-between items-center'>
          <div className='text-blue-700'>
            已选择 <span className='font-semibold'>{selectedRowKeys.length}</span> 项
          </div>
          <div>
            <Space>
              <Popconfirm title='确认批量删除' description={`确定要删除选中的 ${selectedRowKeys.length} 个分组吗？`} onConfirm={handleBatchDelete} okText='确认' cancelText='取消'>
                <Button type='primary' danger icon={<DeleteOutlined />} size='small'>
                  批量删除
                </Button>
              </Popconfirm>
              <Button size='small' onClick={handleClearSelection}>
                取消选择
              </Button>
            </Space>
          </div>
        </div>
      )}

      {/* 表格 */}
      <div className='bg-white rounded-lg shadow-sm border border-gray-200'>
        <Table
          columns={columns}
          dataSource={data}
          loading={loading}
          rowKey='id'
          rowSelection={{
            selectedRowKeys,
            onChange: handleSelectionChange,
            // 使用自定义的跨页选择逻辑，不使用 Antd 内置的
          }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange,
            pageSizeOptions: ['20', '30', '50', '100'],
          }}
          size='small'
          className='rounded-lg overflow-hidden  px-2'
          scroll={{ y: 400 }}
        />
      </div>

      {/* 分组表单弹窗 */}
      <GroupFormModal visible={formVisible} editingData={editingGroup} onCancel={handleFormCancel} onSubmit={handleFormSubmit} />
    </Modal>
  );
};

export default GroupManagementModal;
